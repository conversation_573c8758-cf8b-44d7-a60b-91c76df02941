import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface Destination {
  destinationId: string;
  destinationName: string;
  popular?: boolean;
  isDomestic: boolean;
}

interface WherePanelProps {
  onLocationSelect: (location: string, destinationId: string) => void;
}

export const WherePanel: React.FC<WherePanelProps> = ({ onLocationSelect }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [allDestinations, setAllDestinations] = useState<Destination[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const abortRef = useRef<AbortController | null>(null);

  const FEATURED_DESTINATIONS = [
    { name: 'Goa', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800' },
    { name: 'Kashmir', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800' },
    { name: '<PERSON><PERSON>', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800' },
    { name: 'Ooty', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800' },
    { name: 'Munnar', tag: 'TRENDING', color: 'bg-blue-100 text-blue-800' },
    { name: 'Andaman', tag: 'IN SEASON', color: 'bg-green-100 text-green-800' },
    { name: 'Kodaikanal', tag: 'IN SEASON', color: 'bg-orange-100 text-orange-800' },
    { name: 'Coorg', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800' },
    { name: 'Alleppey', tag: 'BACKWATERS', color: 'bg-green-100 text-green-800' },
    { name: 'Kochi', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800' },
    { name: 'Shimla', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800' },
    { name: 'Bali', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800' },
    { name: 'Maldives', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800' },
  ];

  const isPopularDestination = (name: string) => {
    return FEATURED_DESTINATIONS.some(featured =>
      name.toLowerCase().includes(featured.name.toLowerCase())
    );
  };

  // Fetch destinations from API
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        // Abort previous request if any
        if (abortRef.current) {
          abortRef.current.abort();
        }
        const controller = new AbortController();
        abortRef.current = controller;

        setIsLoading(true);
        let url = 'https://api.tripxplo.com/v1/api/user/package/destination/search';

        if (searchQuery.trim() !== '') {
          url += `?search=${encodeURIComponent(searchQuery)}`;
        }

        const response = await fetch(url, { signal: controller.signal });
        const data = await response.json();
        if (data.result) {
          const destinations = data.result.map((dest: any) => ({
            ...dest,
            popular: isPopularDestination(dest.destinationName),
            isDomestic: dest.destinationType === 'Domestic',
          }));
          setAllDestinations(destinations);
        }
      } catch (error: any) {
        if (error?.name !== 'AbortError') {
          console.error('Error fetching destinations:', error);
        }
      } finally {
        setIsLoading(false);
        abortRef.current = null;
      }
    };

    const timer = setTimeout(() => {
      fetchDestinations();
    }, 300); // Debounce search

    return () => {
      clearTimeout(timer);
      if (abortRef.current) {
        abortRef.current.abort();
      }
    };
  }, [searchQuery]);

  const domesticDestinations = allDestinations.filter(d => d.isDomestic);
  const internationalDestinations = allDestinations.filter(d => !d.isDomestic);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Where to?</h3>

      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="Search destinations..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Results */}
      <div className="max-h-64 overflow-y-auto">
        {isLoading ? (
          <div className="space-y-2">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="animate-pulse h-12 bg-slate-200 rounded-lg"></div>
            ))}
          </div>
        ) : allDestinations.length > 0 ? (
          <>
            {/* Domestic Section */}
            {domesticDestinations.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-bold text-blue-600 mb-2 px-3 py-2 bg-blue-50 rounded-lg">
                  DOMESTIC DESTINATIONS
                </h4>
                <div className="space-y-1">
                  {domesticDestinations.map((dest) => {
                    const featured = FEATURED_DESTINATIONS.find(f =>
                      f.name.toLowerCase() === dest.destinationName.toLowerCase()
                    );
                    return (
                      <button
                        key={dest.destinationId}
                        onClick={() => onLocationSelect(dest.destinationName, dest.destinationId)}
                        className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors text-left"
                      >
                        <div className="flex items-center gap-3">
                          <MapPin className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-700">{dest.destinationName}</span>
                        </div>
                        {featured && (
                          <span className={`text-xs font-bold px-2 py-1 rounded-full ${featured.color}`}>
                            {featured.tag}
                          </span>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* International Section */}
            {internationalDestinations.length > 0 && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-bold text-green-600 mb-2 px-3 py-2 bg-green-50 rounded-lg">
                  INTERNATIONAL DESTINATIONS
                </h4>
                <div className="space-y-1">
                  {internationalDestinations.map((dest) => {
                    const featured = FEATURED_DESTINATIONS.find(f =>
                      f.name.toLowerCase() === dest.destinationName.toLowerCase()
                    );
                    return (
                      <button
                        key={dest.destinationId}
                        onClick={() => onLocationSelect(dest.destinationName, dest.destinationId)}
                        className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors text-left"
                      >
                        <div className="flex items-center gap-3">
                          <MapPin className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-700">{dest.destinationName}</span>
                        </div>
                        {featured && (
                          <span className={`text-xs font-bold px-2 py-1 rounded-full ${featured.color}`}>
                            {featured.tag}
                          </span>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <MapPin className="mx-auto h-8 w-8 text-gray-400 mb-2" />
            <p className="text-sm text-gray-500">No destinations found</p>
          </div>
        )}
      </div>
    </div>
  );
};
