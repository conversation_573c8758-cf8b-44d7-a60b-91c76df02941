"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search } from 'lucide-react';
import { WherePanel } from './search/WherePanel';
import { DatePanel } from './search/DatePanel';
import { WhoPanel } from './search/WhoPanel';
import { useDispatch, useSelector } from 'react-redux';
import { changeDate, changeDestination, changeDestinationId } from '@/app/store/features/searchPackageSlice';
import { selectAdultsChild } from '@/app/store/features/roomCapacitySlice';
import { selectTheme, selectThemeId } from '@/app/store/features/selectThemeSlice';

type ActiveSection = 'where' | 'date' | 'who' | null;

interface SearchBarState {
  location: string;
  locationId: string;
  date: string;
  guests: number;
}

const SearchBar: React.FC = () => {
  const [activeSection, setActiveSection] = useState<ActiveSection>(null);
  const [searchData, setSearchData] = useState<SearchBarState>({
    location: 'Search destinations',
    locationId: '',
    date: 'Add dates',
    guests: 0
  });

  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setActiveSection(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const [dropdownStyle, setDropdownStyle] = useState({});

  const handleSectionClick = (section: ActiveSection, event: React.MouseEvent<HTMLDivElement>) => {
    setActiveSection(activeSection === section ? null : section);
    if (activeSection !== section) {
      const target = event.currentTarget;
      const container = containerRef.current;
      if (target && container) {
        const targetRect = target.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        setDropdownStyle({
          left: `${targetRect.left - containerRect.left}px`,
        });
      }
    }
  };

  const handleLocationSelect = (location: string, locationId: string) => {
    setSearchData(prev => ({ ...prev, location, locationId }));
    setActiveSection('date');
  };

  const handleDateSelect = (date: string) => {
    setSearchData(prev => ({ ...prev, date }));
    setActiveSection('who');
  };

  const handleGuestsChange = (guests: number) => {
    setSearchData(prev => ({ ...prev, guests }));
  };

  const router = useRouter();
  const dispatch = useDispatch();

  const {
    totalAdults: adults,
    totalChilds: children,
    totalRooms: rooms,
  } = useSelector((state: any) => state.roomSelect?.room) || {
    totalAdults: 2,
    totalChilds: 0,
    totalRooms: 1,
  };

  const { theme: selectedTheme, themeId: selectedThemeId } =
    useSelector((state: any) => state.themeSelect) || {
      theme: 'Couple',
      themeId: '',
    };


  const handleSearch = () => {
    // Prepare final data
    const finalData = {
      destination: searchData.location,
      destinationId: searchData.locationId,
      date: searchData.date,
      rooms: rooms,
      adults: adults,
      children: children,
      theme: selectedTheme,
      themeId: selectedThemeId,
    };

    // Dispatch to Redux store
    dispatch(changeDestination(finalData.destination));
    dispatch(changeDestinationId(finalData.destinationId));
    if (finalData.date && finalData.date !== 'Add dates') {
      dispatch(changeDate(new Date(finalData.date).toISOString()));
    }

    // Dispatch theme to Redux store
    dispatch(selectTheme({ selectedTheme: finalData.theme }));
    dispatch(selectThemeId({ selectedThemeId: finalData.themeId }));

    // Dispatch room and traveler data to Redux store
    dispatch(
      selectAdultsChild({
        room: {
          adult: finalData.adults,
          child: finalData.children,
          room: finalData.rooms,
        },
      })
    );

    // Navigate to packages page
    router.push('/packages');
  };

  const getSectionClasses = (section: ActiveSection) => {
    const baseClasses = "flex-1 px-6 py-3 text-left transition-all duration-300 ease-smooth cursor-pointer";
    const isActive = activeSection === section;

    if (isActive) {
      return `${baseClasses} bg-search-active shadow-elevated rounded-full z-10 relative`;
    }

    return `${baseClasses} hover:bg-white/50 rounded-full`;
  };

  const getGuestText = () => {
    if (searchData.guests === 0) return 'Add guests';
    if (searchData.guests === 1) return '1 guest';
    return `${searchData.guests} guests`;
  };

  return (
    <section id="hero-section" className="relative w-full h-[500px] sm:h-[600px] min-h-[500px] sm:min-h-[600px] flex flex-col items-center justify-center">
      {/* Background with a more vibrant and inviting travel image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat h-full"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1512100356356-de1b84283e18?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/70 h-full"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 max-w-5xl mx-auto">
        <h1 className="text-4xl md:text-7xl font-semibold text-white mb-4 leading-tight animate-fade-in-up">
          Seamless Travel, <span className="text-emerald-400 font-extrabold italic">Unforgettable</span> Experiences
        </h1>

        {/* New SearchBar Component */}
        <div className="w-full max-w-4xl mx-auto mb-12 mt-8">
          <div ref={containerRef} className="relative w-full max-w-4xl mx-auto">
            {/* Main Search Container */}
            <div className="bg-search-container rounded-full shadow-soft border border-gray-200/50 flex items-center pr-1 relative z-20">
              {/* Where Section */}
              <div
                className={`${getSectionClasses('where')} mr-2`}
                onClick={(e) => handleSectionClick('where', e)}
              >
                <div className="text-xs font-semibold text-search-text mb-1">Where</div>
                <div className="text-sm text-search-secondary truncate">
                  {searchData.location}
                </div>
              </div>

              {/* Divider */}
              <div className="w-px h-8 bg-gray-300/50" />

              {/* Date Section */}
              <div
                className={getSectionClasses('date')}
                onClick={(e) => handleSectionClick('date', e)}
              >
                <div className="text-xs font-semibold text-search-text mb-1">Date</div>
                <div className="text-sm text-search-secondary">
                  {searchData.date}
                </div>
              </div>

              {/* Divider */}
              <div className="w-px h-8 bg-gray-300/50" />

              {/* Who Section */}
              <div
                className={getSectionClasses('who')}
                onClick={(e) => handleSectionClick('who', e)}
              >
                <div className="text-xs font-semibold text-search-text mb-1">Who</div>
                <div className="text-sm text-search-secondary">
                  {getGuestText()}
                </div>
              </div>

              {/* Search Button */}
              <div className="px-2">
                <button
                  onClick={handleSearch}
                  className={`
                    bg-app-secondary text-search-accent-foreground rounded-full flex items-center justify-center
                    transition-all duration-300 ease-smooth hover:shadow-md
                    ${activeSection === 'who'
                      ? 'px-6 py-3 gap-2'
                      : 'w-12 h-12'
                    }
                  `}
                >
                  <Search className="w-4 h-4" />
                  {activeSection === 'who' && (
                    <span className="text-sm font-semibold whitespace-nowrap">
                      Search
                    </span>
                  )}
                </button>
              </div>
            </div>

            {/* Dropdown Panel */}
            {activeSection && (
              <div className="absolute top-full left-0 right-0 mt-2 z-10">
                <div className="bg-white rounded-3xl shadow-strong border border-gray-200/50 p-6 animate-in slide-in-from-top-2 fade-in duration-300 w-[375px]">
                  {activeSection === 'where' && (
                    <WherePanel onLocationSelect={handleLocationSelect} />
                  )}
                  {activeSection === 'date' && (
                    <DatePanel onDateSelect={handleDateSelect} />
                  )}
                  {activeSection === 'who' && (
                    <WhoPanel
                      guests={searchData.guests}
                      onGuestsChange={handleGuestsChange}
                    />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Enhanced floating elements for a more dynamic feel */}
      <div className="absolute top-20 left-10 w-28 h-28 bg-emerald-400/20 backdrop-blur-sm rounded-full animate-pulse-slow hidden lg:block"></div>
      <div className="absolute bottom-32 right-16 w-24 h-24 bg-blue-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-700 hidden lg:block"></div>
      <div className="absolute top-1/3 right-24 w-20 h-20 bg-purple-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-300 hidden lg:block"></div>
      <div className="absolute bottom-1/4 left-24 w-16 h-16 bg-yellow-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-1000 hidden lg:block"></div>
    </section>
  );
};



export default SearchBar;
