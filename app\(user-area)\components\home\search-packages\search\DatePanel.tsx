import React, { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { startOfToday } from 'date-fns';

interface DatePanelProps {
  onDateSelect: (date: string) => void;
}

export const DatePanel: React.FC<DatePanelProps> = ({ onDateSelect }) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(startOfToday());

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      onDateSelect(date.toLocaleDateString());
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">When?</h3>

      <div className="w-full">
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={handleDateSelect}
          disabled={(date) => date < startOfToday()}
          initialFocus
          className="w-full"
          classNames={{
            table: "w-full border-spacing-1",
            head_cell: "w-full md:w-[40px] font-semibold text-xs md:text-sm text-gray-700 pb-2 text-center",
            cell: "w-full h-8 sm:w-[40px] md:h-[40px] lg:w-[40px] md:h-[40px] p-0.5",
            row: "flex w-full justify-stretch gap-1",
            day: "rounded-lg w-full h-full text-xs md:text-sm font-medium hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 hover:scale-105 hover:shadow-sm flex items-center justify-center",
            day_selected: "bg-search-accent text-white rounded-lg shadow-lg hover:shadow-xl transform scale-105",
            day_today: "bg-gradient-to-br from-[#23cd92] to-[#1fb584] text-white rounded-lg shadow-md",
            day_disabled: "opacity-40 cursor-not-allowed hover:bg-transparent hover:text-gray-400 hover:scale-100",
            caption_label: "text-base md:text-lg font-bold text-gray-800 mb-2",
            nav_button: "rounded-full w-6 h-6 md:w-8 md:h-8 hover:bg-gray-100 transition-all duration-200 hover:scale-110 border border-gray-200 shadow-sm",
            nav_button_previous: "hover:bg-blue-50 hover:border-blue-200",
            nav_button_next: "hover:bg-blue-50 hover:border-blue-200",
            caption: "flex justify-center items-center mb-4 relative",
          }}
        />
      </div>
    </div>
  );
};
